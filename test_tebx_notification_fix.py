#!/usr/bin/env python3
"""
Test script to verify that the TEBX transaction notification system is working correctly
after implementing the claim verification system.
"""

import os
import sys
import re
from datetime import datetime

def check_code_pattern(filepath, pattern, description):
    """Check if a code pattern exists in a file"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
            if re.search(pattern, content, re.MULTILINE | re.DOTALL):
                return True, f"✅ {description} found in {filepath}"
            else:
                return False, f"❌ {description} not found in {filepath}"
    except Exception as e:
        return False, f"❌ Error reading {filepath}: {e}"

def verify_tebx_notification_fix():
    """Verify that the TEBX notification system is properly restored"""
    print("🔍 TEBX TRANSACTION NOTIFICATION SYSTEM VERIFICATION")
    print("=" * 70)
    print(f"Verification started at: {datetime.now()}")
    
    all_checks_passed = True
    
    # Check 1: Interactive view creation in process_payment_notification
    print("\n🎯 Checking interactive view creation...")
    interactive_patterns = [
        (r"PersistentTebexTransactionView.*notification.*True", "Interactive view creation with claim button"),
        (r"view\.create_compact_embed", "Embed creation through view"),
        (r"await channel\.send.*embed.*view", "Sending embed with interactive view"),
        (r"view\.message = sent_message", "Message reference assignment"),
        (r"register_persistent_view.*view", "Persistent view registration"),
        (r"bot\.add_view.*view", "View registration with bot")
    ]
    
    for pattern, description in interactive_patterns:
        passed, message = check_code_pattern("bot.py", pattern, description)
        print(f"  {message}")
        if not passed:
            all_checks_passed = False
    
    # Check 2: Fallback mechanism
    print("\n🛡️ Checking fallback mechanism...")
    fallback_patterns = [
        (r"except.*Failed to send interactive notification", "Error handling for interactive send"),
        (r"basic_embed.*Transaction Received", "Fallback embed creation"),
        (r"fallback mode.*WARNING", "Fallback logging")
    ]
    
    for pattern, description in fallback_patterns:
        passed, message = check_code_pattern("bot.py", pattern, description)
        print(f"  {message}")
        if not passed:
            all_checks_passed = False
    
    # Check 3: Store information preservation
    print("\n🏪 Checking store information preservation...")
    store_patterns = [
        (r"store.*data\['store'\]", "Store information added to transaction data"),
        (r"data\['store'\].*has received a payment", "Store name in fallback embed")
    ]
    
    for pattern, description in store_patterns:
        passed, message = check_code_pattern("bot.py", pattern, description)
        print(f"  {message}")
        if not passed:
            all_checks_passed = False
    
    # Check 4: Button functionality preservation
    print("\n🔘 Checking button functionality...")
    button_patterns = [
        (r"View Details.*discord\.ui\.button", "View Details button exists"),
        (r"Mark as Claimed.*discord\.ui\.button", "Mark as Claimed button exists"),
        (r"mark_claimed_button.*async def", "Claim button callback exists")
    ]
    
    for pattern, description in button_patterns:
        passed, message = check_code_pattern("bot.py", pattern, description)
        print(f"  {message}")
        if not passed:
            all_checks_passed = False
    
    # Check 5: Webhook processing flow
    print("\n📡 Checking webhook processing flow...")
    webhook_patterns = [
        (r"message\.webhook_id.*tebex_channel", "Webhook detection"),
        (r"has received a payment.*process_payment_notification", "Payment notification trigger"),
        (r"parts = content\.split.*╽", "Webhook content parsing"),
        (r"save_transaction.*transaction_data", "Transaction data saving")
    ]
    
    for pattern, description in webhook_patterns:
        passed, message = check_code_pattern("bot.py", pattern, description)
        print(f"  {message}")
        if not passed:
            all_checks_passed = False
    
    # Check 6: Persistent view functionality
    print("\n🔄 Checking persistent view functionality...")
    persistent_patterns = [
        (r"class PersistentTebexTransactionView.*TebexTransactionView", "Persistent view class inheritance"),
        (r"timeout = None", "Never timeout setting"),
        (r"_load_claim_data_from_transaction", "Claim data loading"),
        (r"_save_claim_to_database", "Claim data saving")
    ]
    
    for pattern, description in persistent_patterns:
        passed, message = check_code_pattern("bot.py", pattern, description)
        print(f"  {message}")
        if not passed:
            all_checks_passed = False
    
    # Final result
    print("\n" + "=" * 70)
    if all_checks_passed:
        print("🎉 ALL CHECKS PASSED! TEBX notification system is fully restored!")
        print("\n✅ System Status:")
        print("  1. ✅ Interactive buttons restored in TEBX notifications")
        print("  2. ✅ Persistent view functionality working")
        print("  3. ✅ Claim verification system integrated")
        print("  4. ✅ Fallback mechanism in place")
        print("  5. ✅ Webhook processing flow intact")
        print("  6. ✅ Store information preserved")
        print("\n🚀 Expected Workflow:")
        print("  1. TEBX sends webhook → Bot processes notification")
        print("  2. Interactive embed with buttons sent to channel")
        print("  3. Admins can click 'View Details' and 'Mark as Claimed'")
        print("  4. Claims are saved to database with admin info")
        print("  5. Verification status shows in lookup/validate commands")
        print("  6. Views persist across bot restarts")
    else:
        print("❌ SOME CHECKS FAILED! Please review the issues above.")
        print("\n🔧 Troubleshooting:")
        print("  1. Check that process_payment_notification uses PersistentTebexTransactionView")
        print("  2. Verify interactive view is sent with embed")
        print("  3. Ensure fallback mechanism is in place")
        print("  4. Check button functionality is preserved")
    
    print(f"\nVerification completed at: {datetime.now()}")
    return all_checks_passed

if __name__ == "__main__":
    success = verify_tebx_notification_fix()
    sys.exit(0 if success else 1)
